/**
 * Monorepo 版本管理脚本
 *
 * 使用standard-version自动管理版本号、生成changelog和创建git tag
 * 支持为每个游戏项目独立管理版本
 *
 * 使用方法:
 * node scripts/version.js [game-name] [release-type]
 *
 * game-name: shark, pirate, dixit 或 all (默认: all)
 * release-type: patch, minor, major, prerelease (可选，自动判断)
 *
 * 示例:
 * node scripts/version.js shark patch     # 为小心鲨手发布补丁版本
 * node scripts/version.js pirate          # 为海盗桶自动判断版本类型
 * node scripts/version.js all             # 为所有有变更的游戏发布版本
 *
 * 环境变量:
 * DRY_RUN - 设置为true时只预览不实际执行 (默认: false)
 * SKIP_CHANGELOG - 设置为true时跳过changelog生成 (默认: false)
 * SKIP_COMMIT - 设置为true时跳过git commit (默认: false)
 * SKIP_TAG - 设置为true时跳过git tag (默认: false)
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 从环境变量获取配置
const isDryRun = process.env.DRY_RUN === 'true'
const skipChangelog = process.env.SKIP_CHANGELOG === 'true'
const skipCommit = process.env.SKIP_COMMIT === 'true'
const skipTag = process.env.SKIP_TAG === 'true'

// 获取命令行参数
const [, , gameName = 'all', releaseType] = process.argv

// 动态获取游戏配置
function getGameConfigs() {
    const packagesDir = path.join(process.cwd(), 'packages')
    const games = {}

    try {
        const entries = fs.readdirSync(packagesDir, { withFileTypes: true })

        for (const entry of entries) {
            if (entry.isDirectory() && entry.name.startsWith('game-')) {
                const gamePath = path.join(packagesDir, entry.name)
                const packageJsonPath = path.join(gamePath, 'package.json')

                // 从目录名提取游戏简称 (去掉 'game-' 前缀)
                const gameKey = entry.name.replace('game-', '')

                let displayName = gameKey // 默认显示名称

                // 尝试从 package.json 读取显示名称
                try {
                    if (fs.existsSync(packageJsonPath)) {
                        const packageJson = JSON.parse(
                            fs.readFileSync(packageJsonPath, 'utf8')
                        )
                        displayName =
                            packageJson.displayName ||
                            packageJson.description ||
                            gameKey
                    }
                } catch (error) {
                    console.warn(
                        `警告: 无法读取 ${entry.name}/package.json:`,
                        error.message
                    )
                }

                games[gameKey] = {
                    name: entry.name,
                    displayName: displayName,
                    path: `packages/${entry.name}`,
                    tagPrefix: `${entry.name}@`,
                }
            }
        }
    } catch (error) {
        console.error('错误: 无法读取 packages 目录:', error.message)
        process.exit(1)
    }

    return games
}

// 游戏配置
const GAMES = getGameConfigs()

// 检查是否在git仓库中
function checkGitRepository() {
    try {
        execSync('git rev-parse --git-dir', { stdio: 'ignore' })
        return true
    } catch (error) {
        console.error('错误: 当前目录不是git仓库')
        return false
    }
}

// 检查工作目录是否干净
function checkWorkingDirectory() {
    try {
        const status = execSync('git status --porcelain', { encoding: 'utf8' })
        if (status.trim()) {
            console.error('错误: 工作目录不干净，请先提交或暂存所有更改')
            console.log('未提交的更改:')
            console.log(status)
            return false
        }
        return true
    } catch (error) {
        console.error('错误: 无法检查git状态:', error.message)
        return false
    }
}

// 获取游戏当前版本
function getCurrentVersion(gameConfig) {
    try {
        const packagePath = path.join(gameConfig.path, 'package.json')
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
        return packageJson.version || '0.1.0'
    } catch (error) {
        console.error(
            `错误: 无法读取${gameConfig.path}/package.json:`,
            error.message
        )
        return '0.1.0'
    }
}

// 检查游戏是否有变更
function hasGameChanges(gameConfig, sinceTag) {
    try {
        // 检查自上次tag以来是否有该游戏相关的变更
        const command = sinceTag
            ? `git diff --name-only ${sinceTag}..HEAD`
            : `git diff --name-only HEAD~10..HEAD` // 检查最近10个commit

        const changedFiles = execSync(command, {
            encoding: 'utf8',
            stdio: 'pipe',
        })
            .split('\n')
            .filter((file) => file.trim())

        // 检查是否有该游戏目录下的文件变更
        const hasChanges = changedFiles.some(
            (file) =>
                file.startsWith(gameConfig.path + '/') ||
                file.startsWith('packages/pitayaclient/') ||
                file.startsWith('packages/sgc/') ||
                file === '.gitlab-ci.yml'
        )

        return hasChanges
    } catch (error) {
        console.log(
            `检查${gameConfig.displayName}变更时出错，默认认为有变更:`,
            error.message
        )
        return true
    }
}

// 获取最新的游戏tag
function getLatestGameTag(gameConfig) {
    try {
        const tags = execSync(
            `git tag -l "${gameConfig.tagPrefix}*" --sort=-version:refname`,
            {
                encoding: 'utf8',
                stdio: 'pipe',
            }
        ).trim()

        return tags.split('\n')[0] || null
    } catch (error) {
        return null
    }
}

// 构建standard-version命令
function buildStandardVersionCommand(gameConfig) {
    let command = 'npx standard-version'

    // 添加配置文件路径
    command += ` --config-file-name .versionrc.json`

    // 添加release类型
    if (releaseType) {
        command += ` --release-as ${releaseType}`
    }

    // 添加选项
    if (isDryRun) {
        command += ' --dry-run'
    }

    if (skipChangelog) {
        command += ' --skip.changelog'
    }

    if (skipCommit) {
        command += ' --skip.commit'
    }

    if (skipTag) {
        command += ' --skip.tag'
    }

    return command
}

// 执行单个游戏的版本管理
function executeGameVersioning(gameConfig) {
    const currentVersion = getCurrentVersion(gameConfig)
    console.log(`\n🎮 ${gameConfig.displayName}`)
    console.log(`📁 路径: ${gameConfig.path}`)
    console.log(`📦 当前版本: ${currentVersion}`)

    // 检查是否有变更
    const latestTag = getLatestGameTag(gameConfig)
    const hasChanges = hasGameChanges(gameConfig, latestTag)

    if (!hasChanges && !releaseType) {
        console.log(`⏭️  跳过 ${gameConfig.displayName} - 没有检测到相关变更`)
        return { success: true, skipped: true }
    }

    if (isDryRun) {
        console.log('🔍 预览模式 - 不会实际执行更改')
    }

    const command = buildStandardVersionCommand(gameConfig)
    console.log(`🚀 执行命令: ${command}`)
    console.log(`📂 工作目录: ${gameConfig.path}`)

    try {
        // 切换到游戏目录执行
        const originalCwd = process.cwd()
        process.chdir(gameConfig.path)

        const output = execSync(command, {
            encoding: 'utf8',
            stdio: 'inherit',
        })

        let newVersion = currentVersion
        if (!isDryRun) {
            newVersion = getCurrentVersion(gameConfig)
            console.log(`✅ ${gameConfig.displayName} 版本管理完成!`)
            console.log(`📦 新版本: ${newVersion}`)
        }

        // 切换回原目录
        process.chdir(originalCwd)

        return {
            success: true,
            skipped: false,
            oldVersion: currentVersion,
            newVersion: newVersion,
            gameConfig: gameConfig,
        }
    } catch (error) {
        console.error(
            `❌ ${gameConfig.displayName} 版本管理失败:`,
            error.message
        )
        // 确保切换回原目录
        try {
            process.chdir(path.resolve('.'))
        } catch (e) {}
        return { success: false, skipped: false, gameConfig: gameConfig }
    }
}

// 执行版本管理
function executeVersioning() {
    const results = []

    if (gameName === 'all') {
        console.log('🚀 开始为所有游戏执行版本管理...')

        // 为所有游戏执行版本管理
        for (const [key, gameConfig] of Object.entries(GAMES)) {
            const result = executeGameVersioning(gameConfig)
            results.push(result)
        }
    } else {
        // 为指定游戏执行版本管理
        const gameConfig = GAMES[gameName]
        if (!gameConfig) {
            console.error(`❌ 未知的游戏名称: ${gameName}`)
            console.log(`可用的游戏: ${Object.keys(GAMES).join(', ')}`)
            return false
        }

        console.log(`🚀 开始为 ${gameConfig.displayName} 执行版本管理...`)
        const result = executeGameVersioning(gameConfig)
        results.push(result)
    }

    // 生成汇总结果
    if (!isDryRun) {
        const successResults = results.filter((r) => r.success && !r.skipped)
        const skippedResults = results.filter((r) => r.skipped)
        const failedResults = results.filter((r) => !r.success)

        console.log('\n📊 版本管理汇总:')
        console.log(`✅ 成功: ${successResults.length}`)
        console.log(`⏭️  跳过: ${skippedResults.length}`)
        console.log(`❌ 失败: ${failedResults.length}`)

        if (successResults.length > 0) {
            // 输出结果到文件，供CI使用
            const resultFile = path.join(process.cwd(), 'version-result.json')
            const resultData = {
                games: successResults.map((r) => ({
                    name: r.gameConfig.name,
                    displayName: r.gameConfig.displayName,
                    oldVersion: r.oldVersion,
                    newVersion: r.newVersion,
                    tagPrefix: r.gameConfig.tagPrefix,
                })),
                releaseType: releaseType || 'auto',
                timestamp: new Date().toISOString(),
            }

            fs.writeFileSync(resultFile, JSON.stringify(resultData, null, 2))
            console.log(`📄 版本信息已保存到: ${resultFile}`)
        }
    }

    return results.every((r) => r.success)
}

// 主函数
function main() {
    console.log('🚀 开始版本管理...')

    // 检查git仓库
    if (!checkGitRepository()) {
        process.exit(1)
    }

    // 检查工作目录（仅在非预览模式下）
    if (!isDryRun && !checkWorkingDirectory()) {
        process.exit(1)
    }

    // 执行版本管理
    if (!executeVersioning()) {
        process.exit(1)
    }

    console.log('✨ 版本管理完成!')
}

// 如果直接运行此脚本
if (require.main === module) {
    main()
}

module.exports = {
    GAMES,
    checkGitRepository,
    checkWorkingDirectory,
    getCurrentVersion,
    hasGameChanges,
    getLatestGameTag,
    buildStandardVersionCommand,
    executeGameVersioning,
    executeVersioning,
}
