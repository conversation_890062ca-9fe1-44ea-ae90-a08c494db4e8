# 小心鲨手 - 更新日志

本项目的所有重要更改都将记录在此文件中。


### 0.1.1 (2025-05-28)


### 👷 CI/CD

* 海盗桶游戏ci构建发布流程 ([09671c5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/09671c5f718260bc947326bcf0152ca074211c2f))
* 海盗桶游戏ci构建发布流程 ([cfc57a7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cfc57a7d4d7fd38133e5c18347f0c6ab09018836))
* 降低编辑器版本试试 ([33a4efd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/33a4efdba8413f6e9823d19e974023328d21df43))
* 联调环境部署及配置文件 ([e05d516](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e05d5166e9c5b51ae1ea31515e9de604a973d965))
* 联调环境部署及配置文件 ([52d0912](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/52d0912aa2b7e104ed514e1deca1675eb518aa4e))
* 暂时移除 gameapi 依赖，后续需要重新规划 ([5ae3c5b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5ae3c5b3bbc52e78010c8998c3562cd863bb297d))
* 支持打出不同目录层级的zip包给语音房使用 ([ad3c4c7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ad3c4c75b702a53972efb34c007b7427dbbcf047))
* 支持打出不同目录层级的zip包给语音房使用 ([eba8d38](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/eba8d38b0e92c5b6661de7d42869d95c23434b74))
* 支持打出不同目录层级的zip包给语音房使用 ([150be54](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/150be54ab3af9b768f81ce3f05cac544f15250e3))
* 支持打出不同目录层级的zip包给语音房使用 ([2614803](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2614803d7b7a7b405c242672086385b744b656b1))
* 支持打出不同目录层级的zip包给语音房使用 ([1ade643](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1ade64360cd99f47e4bc785adc32c8df2d0ac324))
* 支持打出不同目录层级的zip包给语音房使用 ([c8f2d86](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c8f2d86b4bd276bb1725f7076efe55c3481f57cb))
* build ([7d8107f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7d8107f9c2baa7d66d0313258ff11e2082666b9a))
* build ([6de76ba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6de76ba58a35d13fbdd3a124d17140aceb692d53))
* build ([7588cda](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7588cda783f396f2a47360236df640f5f9bb4538))
* build ([a5e2ef0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a5e2ef00860230c52fb3292464255462c3b688bd))
* build ([b30d5bb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b30d5bb63bb17ec8c300d9401c61370a1f952401))
* build ([3e2539d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3e2539d5a5999e5721d933e0cbc4ab2aea162bdd))
* build ([a81fdef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a81fdef856266717c6d7aec38d8f9fc927283570))
* build ([1f8cf2f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1f8cf2f1be2019cdff75199972a4a35817f42713))
* build ([d6c3899](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d6c3899311c836a68a46e5aaf490942b56f07949))
* build ([8e085bf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e085bfdbecd8e3123479861d14517b5bd587a38))
* build ([1b1db3e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1b1db3ea87342e59e820bde3c262378c65fbe6f8))
* build ([907aefc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/907aefc385f74ceb629da4e1262a2d9c8520fcbf))
* build ([53c30d6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/53c30d65dd3bd429ae1755a38b5d071dbc1684b3))
* build ([14fd54f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/14fd54f41ac8e4987c3198e2caef119b410849c6))
* build ([cc67409](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cc67409f385f59c7353374240de03fe0b84b5453))
* build ([c7723ea](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c7723eaa3d12c64457279e3850dc5b8eec38e219))
* build ([825a090](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/825a09074926a0c1f781b6e4e42259a4ab17f5b0))
* build ([a626c1d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a626c1d594c019e1c53da325a6f08a56f4de022e))
* build ([43e14a5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/43e14a544e3455d435f1f63996294ba469dc2880))
* build ([21e40ef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/21e40ef7d521c2179d2123b624877f0dec32f743))
* build ([2d8b8ee](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2d8b8eecee5f8b01059f22b2fb8ec429f3dafa12))
* build ([c74e7c5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c74e7c53c35f45a9fc4f8636769bb948ebd32209))
* build ([380be65](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/380be650d4c8cdcf9585d5d65cd519cd851efa7d))
* build ([0a28b2d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0a28b2d0577892dee684daba0934bd23d21cc6cf))
* build ([5457a52](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5457a527228cf21db819a0972456b8bdbf9ad0da))
* build ([266b165](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/266b16506ef38f27448680e435316787c71f5717))
* build ([d675d85](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d675d8510c3919d6961ff10f8d3f66c43e6fe417))
* build ([54fe983](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/54fe983695cfc24560136d401492384d319c26e5))
* build ([78fadf5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/78fadf5d40f5502d456ca3cd4ccfd6eaed89c00b))
* build ([c851914](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c85191475815ac9f8263b66de489730faff9097a))
* ci 配置文件优化 ([6f34e82](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6f34e82702ea781dff5cf4d7c449b43ee1867185))
* deploy test ([ed76899](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ed768990081ad502c25e53f149b88a1520e3ba7d))
* game_codfe ([b9f034b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b9f034baf39a6ae19942ba203670bdaad3415867))
* install ([fa8126b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa8126bad72ed91f75351a8506f5835f25245d27))
* install ([9216c79](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9216c79eec571ffa1c5ad80c8fde7e050a4d4e2e))
* install ([42b0a0d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/42b0a0d986ede071a60240c89cb000366ab57b08))
* install ([0e56e40](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0e56e400935dea4393e20496c0ac70b4fb55fa59))
* install ([5cf3baa](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5cf3baaf5128cf32b57d274bfba58e17d38ee908))
* install ([a26446f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a26446f3bd10870d9f707d578bbe4e82f44be1d1))
* install ([008981f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/008981f4f43659337b02e78fa9f67b6ed6340877))
* install ([2d89adf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2d89adfe160cf850f0ad73fb94058b45162a19c1))
* install ([5cd48de](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5cd48dea30309e2900103d437f8db37b37e96d03))
* install ([49ea0f4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/49ea0f439990893adb727c24eb6ab10612d2f6a8))
* install ([b5b08af](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b5b08af3b4632292730597e5526520979e8f75dc))
* install ([ab01018](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ab0101891e69386a43763eb8b035e57b6923080e))
* install ([7168aa5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7168aa56637533d0b18150e844a4bbb309e4aecc))
* install ([0b88a82](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0b88a8269cac3578e727f6e69a3c7331797caa2f))
* install ([25b17c8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/25b17c8bdca435e2cddfedbd5448a5f9721fb646))
* install ([c4433d7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4433d7a8094d4952d3ff48b1a2b3e84ca072ffd))
* install ([0adad0e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0adad0e51ff3814282fd465e2418c8d3110000ab))
* install ([3829ce3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3829ce3f113084eb9293bd1252102faed585b5ab))
* install ([07c3f46](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/07c3f465207923d1d9ab873342568b68c7753e2f))
* install & build ([58719ac](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/58719accb46e618e0af8746783734ec4f707b55c))
* release new games ([ebd7ca1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ebd7ca18766ab31bfeb52267f50d7225ef0ce29c))
* WIP: 完善上传脚本 ([75a717e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/75a717e314e089964ad5c5d340a0279ba8623cd8))
* WIP: 支持上传zip包并通知平台服务端 ([5eb31d5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5eb31d5af0487a38dc6eae7c4bbd958db47c380a))
* WIP: 支持上传zip包并通知平台服务端 ([9e50ef3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9e50ef3e9e7c3e98a061853b338563e3845207cc))
* WIP: 支持上传zip包并通知平台服务端 ([d80f1f5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d80f1f5d769be2df25d6d24a1a7326d0c2eb594c))
* WIP: 支持上传zip包并通知平台服务端 ([f441a8a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f441a8ad3757f9310745646ccb9a28a2abd24711))
* WIP: 支持上传zip包并通知平台服务端 ([94c57af](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/94c57af233f246f0663f4c905c134c23bed6071d))
* WIP: 支持上传zip包并通知平台服务端 ([f839247](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f839247e10ed5690f13da0c0d153597928650d0a))
* WIP:通知脚本 ([a6f6fe1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a6f6fe1e337940f9b0895334a79d0381abf52dd2))
* WIP:通知脚本 ([d6b3ec5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d6b3ec5036446e0d64a9b18b95630b7009e4601c))
* WIP:通知脚本 ([340f587](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/340f58764b92f90785dbfe4a50aab1ec135088ec))
* WIP:通知脚本 ([61d4b6b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/61d4b6bcbc4b8c1fbe1d7b174dbf053614e33205))
* WIP:通知脚本 ([747e4a2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/747e4a287f5e872f370d601a8a12ec7392c9f015))
* WIP:通知脚本 ([c54c2d7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c54c2d7477fb9d043c511a7dfb966c9faae94b0b))
* WIP:通知脚本 ([636a82c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/636a82c13eda1c139f95d1b5beb1c11576d052cf))
* zip 层级调整 ([8883f56](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8883f564869597fcad0a8d983261e9490212c1fd))
* zip 层级调整 ([6961bfb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6961bfbbb4378776875f3a2d8de7d707d19cd018))
* zip 层级调整 ([7fda382](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7fda3821582e13651fc8f484c2475d12216da20a))


### ♻️ 代码重构

* 补充 debugout.ts 类型 ([471dcae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/471dcae52ba983fabc3e39ab9a8f13eee5d03c80))
* 抽出计算位置的方法 ([884cb06](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/884cb0612a484218717a04415793779924f9860a))
* 代码简化 ([c09c9e0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c09c9e079ea87838a50313119e695b1bed873f73))
* 简化玩家头像及播放状态动效相关的机制 ([037405d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/037405d21d76c1348060d408ca4f3364c8f65705))
* 卡牌 action 重构 ([0eb64ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0eb64ae4a99f03ba1beac1271eae30809c030ba8))
* 模块引用路径优化 ([456ac21](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/456ac21c581eefe9e6856c882fcf570a5a38e02e))
* 模块引用路径优化 ([f7d9963](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f7d9963340b9cf3bda919defead8e38fc7c31b52))
* 模块引用路径优化 ([4007e97](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4007e973bf8ce234f82a00d4af85a68926051c22))
* 模块引用路径优化 ([f262d70](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f262d70dbbf04190bfb0a2cde5f4ea964a7639ab))
* 模块引用路径优化 ([c3f4b2e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c3f4b2e5b04af625d6b950a31a11237f14a20a8e))
* 梳理core下的依赖关系&移除未用到的模块 ([1f6ea52](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1f6ea52bbd1ba2003cc6369c07e4ffcf614fe924))
* 项目结构和依赖调整 ([5620df2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5620df27cb98faa5598e0a425a5619e11ebb999d))
* 移除未使用的import模块 ([e324daa](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e324daab5034823e8ce54c2971ab4eb72b66f9dc))
* 移除未使用的import模块 ([f89d8c2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f89d8c2db6e85e3c7d85c2f61ccaebec5d222992))
* 移除未使用的import模块 ([bb0127c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bb0127cbf3591b04df6f27f799474208f54aab35))
* 预制件拆分 ([741b496](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/741b496d5346ee40698a9df7ead7ca2dbe00ae4b))
* 预制件拆分 ([bfc8624](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bfc8624be0fff760f500106a9d2418d29b507891))
* 遮罩调整 ([d65ba5e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d65ba5e497677eaf10cdfd7f8e09156077473e57))
* 重构头像组件，拆分出倒计时组件 ([d25e8ba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d25e8ba689429725c801156fb198ca14326addc8))
* 重构装扮 ([a10dd4e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a10dd4ebf01d9f82a307d90285e39d1d44bfd24d))
* 重构plist相关组件，解决类型报错 ([310e5d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/310e5d1d0be5ca66f14cafbad2592c74617e84ca))
* 重命名组件 & 重新挂载 ([8a9ad51](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a9ad5192912699f5e01bab9c21bb479efb5ce68))
* 重写手牌选中相关功能 ([82d0c90](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/82d0c90be691e80c404008b6551e1c60c6165307))
* 重新手牌选中相关功能 ([006b0f5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/006b0f5e4e7062b74970a3e35201501220e250de))
* add comment to cardAniamtionManager ([eb2750a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/eb2750a30e6efbffe7bb301a58010c05a05f8185))
* CardAnimationManager ([721e34b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/721e34b6f306a172f83335f5f85e279de5068ad3))
* CommonDialog 组件统一处理所有弹框组件的关闭 ([34c1f1a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/34c1f1a18c4b043060bd494048131a6066f8f99b))
* CommonDialog 组件统一处理所有弹框组件的关闭 ([8d608f7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d608f73682055316086ecd571dd08129e470244))
* start scene 和对应的挂载脚本迁移目录 ([fbddecb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fbddecb00cb118ef95f87b24d4e57cd46bb49028))
* WIP: 预制件拆分 ([3a1ff49](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3a1ff49f98485c291a5946c2ffe9683baec9bdc4))
* WIP: 预制件拆分 ([396bd9f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/396bd9f2d7bbfc128fa58af715c574bd4e19fbde))
* WIP: 预制件拆分 ([7ba1d33](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7ba1d33a57f81c01e7c3e4ea512a066105f3845c))
* WIP: 预制件拆分 ([66d175a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/66d175a5661cbbc3455a3c3c54446fda507bca22))
* WIP: 预制件拆分 ([cef3eee](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cef3eee36495fef88fcfd45f10ecd0a8a719d7cb))
* WIP: 预制件拆分 ([1638e6f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1638e6f2a32a9c879a806c4efc44b5c27463ef15))
* WIP: 预制件拆分 ([a18fb90](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a18fb908b8d322d889151b755e4d5ab6aa92c396))


### 📦 构建系统

* 纹理压缩配置调整 ([fc73aaf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fc73aaf3aa50078dd45cf8366f59f859db5845a1))
* pb 生成 ([9b28777](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9b2877763bb7dfdb96efe87d8407cf70411afd2e))


### ✨ 新功能

* 3d 模型及相机位置相关调整 ([c50c14a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c50c14aec36924e55a68029d9ac6a8c0a646ab6d))
* 3d 模型及相机位置相关调整 ([12f5395](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/12f5395f09d7400b6bad6f8e7382c7a2c4b4639d))
* 3d 模型及相机位置相关调整 ([5a225b6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5a225b60f8beb5fa80886b16341b448729b4cf0e))
* 3d 模型及相机位置相关调整 ([a6f219f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a6f219f229f7e424fb2f641d2f8879c3a5a7b6cb))
* 3d Prefeb 调整 ([8d44995](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d44995cc442bde6c4b2a5f1ed5f3d51ec74efb7))
* 3d表情调整 ([52083cc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/52083cc16dab9f6e72aa5ce3baf2c03708014a82))
* 3d动画调整 ([ac03efc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ac03efc2bef4eda2ce64d7a6f36baa59f1bde64d))
* 3d动效节点精简 ([abc5229](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/abc52298462e504abbcf597e6354e108751f7d5c))
* 3D摄像机位置、鲨鱼放大位置基准点、玩家头像布局调整 ([18d99dd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/18d99dd908f2bc2d2291483332d230226f47009e))
* 3D摄像机位置、鲨鱼放大位置基准点、玩家头像布局调整 ([d0959b3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d0959b3ff28586f23b56aa47c0a118119c1756b7))
* 按牙/诊断组件在特定时机增加 disableTouch 桥接调用 ([dcc1ce4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dcc1ce40c62ea2449399245903c52bf3d397ec47))
* 按牙按钮根据次数不同显示不同资源 ([3bd28a0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3bd28a073cd2dd51e24643a563cd03674e26b8fe))
* 按牙结束鲨鱼收起时收起选中的手牌 ([2413d3d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2413d3d47d0f37e9117f850de25f9dca67f764ba))
* 按牙界面鲨鱼模型拉近放大 ([1afd2b7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1afd2b70577f7faebc9b6ad59be6bb78eb243008))
* 按牙界面UI布局调整 ([666a69f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/666a69fb868980f630e8ff27b7491a644ca06a07))
* 背景音音量调整 ([48d6585](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/48d6585aa3df1b6fd8b5b279d5033f408d71f36c))
* 本地环境不开启 debugout ([ec16115](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ec16115a0d67857d931e168fc33453d773187a0c))
* 绷带流程 ([678db7d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/678db7da538dac8e90e6c239bb190193c6d0bd53))
* 播放屏幕中间的动效时增加遮罩层 & 增加海盗死亡骨骼动画 ([5281df2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5281df2e50c8b5115f435db2d34b0dfcbe241d6e))
* 播放屏幕中间的动效时增加遮罩层 & 增加海盗死亡骨骼动画 ([14bb1b6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/14bb1b6ff60f51e6d26aab78ce2a8d2eaead71ef))
* 播放屏幕中间的动效时增加遮罩层 & 增加海盗死亡骨骼动画 ([5fa00c7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5fa00c7c9d65bf0b25c989f5b8fb8c6e725b587e))
* 播放屏幕中间的动效时增加遮罩层 & 增加海盗死亡骨骼动画 ([c4400e8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4400e8a12039900bbeb8f5a5d9fc16a0ca22fb0))
* 补充jsbridge文档 ([1c9aa92](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1c9aa92ba055b29a59bd924a25a48ec7f971d129))
* 不再显示小结算信息 ([327debf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/327debf072e7ccfab909008dcc2b27960ba277a6))
* 部分美术资源对接 ([e47756e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e47756e1c34c9d6e91bfd254d0b67237b165dcb6))
* 部分美术资源对接 ([7ef5cb5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7ef5cb5018ffa322b8f7bd8a4b90f33791b8e0e7))
* 部分美术资源对接 ([920a4a0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/920a4a032a7a3941ffec05804c865008bf68339a))
* 操作鲨鱼模型时，下方游戏页面中的“按牙”和“出牌”的按键隐藏 ([a35fbc7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a35fbc701e7a2b57991e2dd17fad302bec684fef))
* 测试本地加载 ([1596d4c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1596d4c87b25f807364e9e83dd75925e24b61041))
* 测试服调整 ([5369c05](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5369c05583b278c44bd04a418f4e446eed6fc5da))
* 测试服调整 ([27fa11f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/27fa11fc1b157f0eb070c795f864689860d1ac44))
* 测试环境 ([cb053cb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cb053cbee35b76cb922923f81ffbb635096c9413))
* 场景背景调整 ([bc8aec7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bc8aec789e055715a29ff6907f99814873a5c82e))
* 场景优化 ([875e0c2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/875e0c2b4e8b72182d548530ed6f7fa51061769d))
* 场景资源替换 ([34d11ee](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/34d11ee1b723c3cfbdf55799e69d79bb8a4c7ccd))
* 唱票环节音效 ([4ca98d5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4ca98d5d703c9e7bc5c4e4b946843ba40c724527))
* 唱票环节音效 ([94d0c55](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/94d0c55fa68af7dad6e75d97c55451e9c83e9793))
* 唱票无人得分处理 ([553642e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/553642eaeb2cbcc34b19ed5ca7576815f9e63d1b))
* 唱票预制件 ([6088d11](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6088d11852984577eb666dd16cea0a01ebecc9dd))
* 超时UI调整 ([68b7171](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/68b7171af29ba525a8a83110856b29734acf57c7))
* 出绷带界面警告条加长 ([d6ca961](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d6ca961fc2f966221ea39e3aca6fc14dcbe8624c))
* 出牌和摸牌动效优化 ([4b00330](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b00330212ef58b82496778f87dc7143d7b4ca7a))
* 出牌区的延时卡牌 ([b6f92de](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b6f92de90cd71b87cbef4e20469b9f919406ee4e))
* 出牌区位置调整 ([f4b4a14](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f4b4a14c5535ed2c387f7aa5c9220c002a9d4fba))
* 处理单个玩家变更的消息，将玩家数据合并到playerinfo里面 ([0347fde](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0347fde27916402b03736ed5b6b108a582c7dc3a))
* 处理玩家信息变更 ([96e4808](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/96e48086da78d27bf17bedc8491e849a13a75244))
* 打开故事书音效调整 ([034e20e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/034e20ee7d01ddb2a20f69328d1a7a9df3cdc0fd))
* 带云朵的背景图位置下移 ([e7e666f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e7e666f54a6cb709a821c68058bbc7bb467a8db3))
* 当url传了 params 参数时，就还是走旧的流程 ([f484ca7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f484ca7a71a548df1b925871bcf028ea8dac6667))
* 刀孔 to 缝隙 ([93d0c4d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/93d0c4dacea2dfa3fafa1e5acc2f9f9eb6bb3c31))
* 刀孔点击时检测处于桶子的正面还是背面 ([a9d87ad](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a9d87adfd6efc96e1c95c811eb5e5ac9b77da780))
* 刀孔调整 ([5841cae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5841cae0d620cb6dc6eba7cb470328626c58578e))
* 刀孔角度计算优化 ([9b3b053](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9b3b053f50e614d59c21bf1fde273aa60a2715a0))
* 刀孔角度计算优化 ([2ca0680](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2ca06805c1ddc8ce375d57b9e4b16a4d1d43a3a7))
* 刀孔支持点击取消选中 ([8540e47](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8540e47e6a60b628b8e4c0b891d04e5b189bb4f1))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([759e570](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/759e570e90434d98a8680504977c1a9a1d37db11))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([948faba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/948fabadca0e9e0097778ac806ad6fbdb54f916d))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([d426fad](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d426fad224333312a6818fa6f25c8ee5a74e3a68))
* 导出日志提取到GUI中，在任何场景下都可以点击 ([0541da2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0541da27eb7adafa0038be8889193147cbd3bc97))
* 导入海盗3d动效 ([2fc86ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2fc86ae10b314512d0e33f3de335c493c7661537))
* 导入音效文件 & 定义 enum ([260e07f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/260e07fae4b7f4296bcc2edcfcdfc6717f7cc89d))
* 导入音效资源及定义 enum ([e7dd4b2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e7dd4b2d2a203dc4f81f142e1852e669a49dc90e))
* 倒计时效果2 ([4620e9e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4620e9ef9fd6367c19583b26892fb86f87d0cc77))
* 倒计时效果2 ([77f4240](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/77f42404864f516c6ec4c4771c71a55a158757a2))
* 倒计时效果2 ([7d14eba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7d14eba3aaf6dc50bc342bad87b5ff73c9e67cc2))
* 倒计时效果2 ([517078a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/517078afe5ed3cc8782cfe176f44150b2fe15673))
* 点按牙齿碰撞检测由射线检测改为球体投射检测，扩大检测范围 ([3b884cc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3b884cc6e09d98946c901a70249e4b9889ebee2b))
* 点击按牙流程优化 ([e262de7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e262de720f10b513fe9abedf6ce1bda453bab5c3))
* 点击打破托管 ([bf80940](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bf809405c5498cd38fe64ff490bc96ea1228e99d))
* 点击摸牌区弹出按牙界面相关调整 ([2b08021](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2b08021731b87544eec519206c8e496a45af5e78))
* 点击摸牌UI也退出托管机制 ([9b0392f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9b0392fe82db0ea17fd888032b187be2cc22e11d))
* 点击退出托管机制 ([581c3c0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/581c3c058a97d2431b545764927386fc4a86bc27))
* 调用closeGame增加参数 ([f4a5a3a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f4a5a3aa9ba3437ad8bce2b4b2a0ef0641b9ead8))
* 调整光照和刀孔的材质 ([e36b159](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e36b159d7a826eb28c1cc662de850eb15f6d4553))
* 调整UI遮罩层不透明度 ([f1e2c41](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f1e2c41bcf4d42585e42026f856d87c81a2c2c85))
* 动画时机优化 ([6298030](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/629803073627f211fe58befe6790c4cd7a2eb192))
* 动态注册 rpc 的方法 & 定义方法类型 & 投票请求调用 vote 方法 ([cfee25b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cfee25b03d4db62dacb813389338b995bdacbe42))
* 动态注册 rpc 的方法 & 定义方法类型 & 投票请求调用 vote 方法 ([1a860d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1a860d1235ae8aaac77b8274d79b740792736ce6))
* 冻结效果优化 ([cc14a0b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cc14a0b6264bb785399a5cbc99055bc1180dc638))
* 对接超时自动出牌的消息 ([ce9a97d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ce9a97dc60b35f03373a2f2ffa54ad46329b42b4))
* 对接超时自动出牌的消息 ([a1bfc3d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a1bfc3d3556b36cac3748354d604a18da651f64b))
* 对接超时自动出牌的消息 ([31677f4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/31677f474b85b03cf34cb01b7b1a33d51163ba2a))
* 对接倒计时 ([b9af324](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b9af32405ecca774598bd458cb2e54d1c5d01e00))
* 对接倒计时 ([e7d46f7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e7d46f7da090eb4776466b004fd2370f2fd9a957))
* 对接机器人 ([85bdaf3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/85bdaf33d0c0a04f2a83e51b072988a76a50a370))
* 对接强化后摸多张牌的状态 ([e6ee3d9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e6ee3d950946abf2f4526be0f7d411f3bc54fb12))
* 对接音效 ([19d127b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/19d127b47706bf85a00230d73a0fe61e932c8c8e))
* 对接音效：故事书、投票、卡牌预览 ([57fb2c1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/57fb2c106e9a0471c422cac2b402ba3033092a27))
* 对局结束优化 ([98ddc5a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/98ddc5a2fd6afaa57fee18dec7ba00c83713ae1e))
* 对局结束优化 ([2bf863e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2bf863e4b4a11ee863666b49b7f16c610084ce31))
* 防御动效 ([5a2ebbe](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5a2ebbe8e08829c1654a69e78b447b90733bf964))
* 放大后的鲨鱼模型位置改为动态计算，更好的适配不同分辨率屏幕 ([cd1ac03](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cd1ac0375a03eea55a4f4cb0f7bce4f8d255d790))
* 非语音房内给用户增加默认头像 ([f2e2141](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f2e2141e10827cf0bf70c644ba7720332af80c06))
* 攻击按钮替换 ([76c3e16](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/76c3e16775c57aeba7879afe37e328bf176b6a0f))
* 攻击和复活相关调整 ([4a70e65](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4a70e650315e411060360ded27077107c11a78f7))
* 攻击和复活相关调整 ([222ef69](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/222ef691b64fd8836bcbaed99bc5d7275f483153))
* 攻击界面收起时机调整 ([6319819](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6319819b7ac2803bb53ed94a07bd349aaac6f160))
* 攻击界面收起时机调整 ([f3376d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f3376d1ae2a47e8510544d883c9497c5233a7766))
* 攻击流程优化 ([c5ed67e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c5ed67ec4d51feff471fc9560c74ce581bda335d))
* 攻击相关音效播放时机调整 ([57c4c61](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/57c4c61766024921ea100c2829db87e7ab4e1f9f))
* 构建开启debug模式 ([2901534](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2901534bb8af73250ca8a1be2933e92ac9749e1f))
* 构建模板 & 日志模式 ([7932271](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/793227101f1f700cefebb2c4b55ca95fbb68d7dc))
* 骨骼动画png压缩 ([eaa7c2b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/eaa7c2bedd68f5d9b5eb1fc3274796d82c230e3d))
* 故事书-说书人昵称处理 ([31c0762](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/31c07620986b964f9de49c84fa34fe1885a37b3c))
* 故事书切换卡牌模式和故事模式增加渐隐渐现转场效果 ([18d7831](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/18d783173452f6e64a30da0440d2244e54775ec2))
* 故事书优化 ([7ac1183](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7ac118397108cf985e3be15b548731a0d8664806))
* 故事书预制件 ([4b72bfe](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b72bfee40c89c08b00f8e6d4ab98cf88bc3cfd2))
* 故事书组件 disableTouch 处理 ([8db51b8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8db51b8b5ba39016583f170ccbccf285007dee19))
* 关闭多点触控 ([d92eda3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d92eda3f573c19d71625533ad43aefee243de204))
* 关闭网络错误提示后，不销毁ws，确保还能继续重连 ([a04197a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a04197aea56afb693857b46dee3c15548fd75b28))
* 关闭阴影 ([4a5d3f4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4a5d3f4a14919be45b7ef0ba821a9323973539f6))
* 关闭阴影 & 锁30帧 ([31e4929](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/31e4929833dfd15e78d45cb972225f05a3ce677c))
* 关闭debugout ([a7449b2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a7449b29456c12bf8a53b4d5242e9c3e94ec200c))
* 观战 & 索取动效 ([d2883e3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d2883e3c144a670b7a04066b62fd1541edc3d058))
* 观战-投票环节 ([e32fe95](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e32fe952782bf6fd5c710abc3322c0bd20c8d89a))
* 光源 ([db637f0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/db637f0499c8752267ff67c3c8d1de83ac1b77f0))
* 光源调整 ([f76c372](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f76c372ddb0e439c4567216ae5f984d0978437a5))
* 光源调整 ([a9a3065](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a9a30651a2366b44a3051c8db215dc1c974913f9))
* 光照 & 阴影 ([5f51706](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5f51706dd4bb7d6d35f4d1b658448c8a5161559f))
* 光照调整 ([a1a9a73](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a1a9a737a04d286d24a9140020824f0c3b10e4a2))
* 光照调整 ([bccacd3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bccacd3d9b1ef5c6e3544378b546f93d49b19889))
* 光照效果调整 ([66d5644](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/66d564472b970c82d4128a72565ee9992b11fdf6))
* 光照阴影调整 ([5b5eb58](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5b5eb58488f06b9c5afe5d17436533b35cc6054f))
* 海盗飞起动画优化 ([8759b7e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8759b7e86ef954ef330679902645d24fd04eb022))
* 海盗桶-背景动效 ([cf25f70](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cf25f7007cf1140d281cf5e9b6daae5ed49ce6f1))
* 海盗桶不再自动旋转回正 ([b65b355](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b65b3557ce1272b44456a4f4a6ebbd9292b43803))
* 海盗桶游戏关闭阴影渲染 ([231a1e2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/231a1e248d87dbbb2e47078432a7a8c233890654))
* 海盗在被攻击时的表情转换 ([9bd815b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9bd815bd84233386225cbf9fcd59d8ebdf4939a0))
* 海盗在被攻击时的表情转换 ([26104db](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/26104db0572f80dab1f2ac406723b9d68e233e01))
* 合图 ([8d8e4c2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d8e4c2e6162de6d819fbe055fdd7cb360f010aa))
* 合图调整 ([ef27b9b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ef27b9b8dd4731c26193766a7951a3766d180fa3))
* 恢复disableTouch桥接 ([73db4e3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/73db4e388ab8a6ad028beba71c9125d6b03d8155))
* 积分榜预制件 ([1423b8c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1423b8c14231b8239bd00447d967b939615ff727))
* 计分榜和故事书图标点击缩放效果 ([4d5b57b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4d5b57b702a36f45ec27ad89624d8c8f054a21ab))
* 简化 breakHosting 调用 ([867a32d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/867a32da1f26ad9b2cb83f3008e4004008142930))
* 交换动画 ([8c9eb14](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8c9eb149543b1450908b0654eadc241ce986ca18))
* 揭示说书人动效：魔法棒+翻牌 ([35c3454](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/35c3454da6606b7c51745b668711aaf0f046b527))
* 结束动画优化 ([123b128](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/123b1289589fc15ba5c754900fa2de603beee2d9))
* 进入摸牌出牌阶段时清空手牌选择状态 ([e8a7d53](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e8a7d53fd59aa0b953bba8ce7ee52de738d1d7d5))
* 进入投票阶段动画 ([dd1acf1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dd1acf10fbed980b79d28349d973d62c424f30fc))
* 进入投票阶段过场 ([70a5d74](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/70a5d74ed74fc2983845b8eea5d93534be2644a7))
* 精简日志 ([e44bf85](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e44bf85ee2fd22626a88b76d5aa81a76bf8b08d4))
* 卡牌尺寸规格 ([3e14f4d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3e14f4d34c8b04e5cf0f50fb9a18cab83e52bcfd))
* 卡牌放大预览功能 ([5d1ea3e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d1ea3e812a07a0fc365c509fe897416d85be183))
* 卡牌加载加入重试机制 ([e94de96](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e94de965682fbde54d545874d7c4fa2beafc69f2))
* 卡牌加载优化-动态获取后缀 ([12008f1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/12008f177c26c8bd4e50dfe9e339ea31b3294c8c))
* 卡牌素材替换 ([2cd33cf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2cd33cfd402c2cf7c3ad9362bfdd1c222d09f035))
* 卡牌替换 & 击中弱点动画调整 ([ec7ab47](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ec7ab47e2f23feae7e59f345bcdbfed376afbeb7))
* 开启纹理压缩 ([de56617](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/de566179f98d0368a13776ce1657207f0894075e))
* 客户端加载zip包调试 ([7582eba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7582ebaf2e67c5b55dc9aa8c5237d3965279e0ae))
* 联调环境支持url动态传入服务地址 ([08bf255](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/08bf2555bf021ecc64520e3b468b3ba3b0059c11))
* 联调环境支持url动态传入服务地址 ([71eecf4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/71eecf4853d3cf00ec5793a2332d348432cf6764))
* 联调环境debugMode ([4bdff05](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4bdff05cf5cec1de24f568bd8e65aa48bbb2079e))
* 联调环境debugMode ([c4d3aef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4d3aefa68b3322682886270bcd009ec0e7842bd))
* 联调环境debugMode ([2a06145](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2a061453b6a3ee77ede898af272f18109a372ef0))
* 流汗动画调整 ([3972271](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/39722718b7cc418614c8772e3e0b7a855e5aa6ad))
* 轮到你了 ([dcbfa05](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dcbfa058d0cd8e24114ad40527f47dfc737bdd62))
* 轮到你了层级优化 ([0b8b653](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0b8b6534965d08d6851e3f4e8a6dca9c5b3b085a))
* 轮到玩家回合相关调整 ([72674ee](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/72674eec2424ba340b3b4d6adb358ce35183d260))
* 麦位表情 ([72b6995](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/72b699576c41c44944b1bbda658daf90187336d9))
* 麦位表情 ([98edc08](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/98edc082c76436447e673452b1c86f0101e65744))
* 麦位表情 ([b42f3b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b42f3b41483311264f0694a14462c85f4bd360ad))
* 摸多种牌不关闭摸牌界面判断条件变更 ([11f1fd8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/11f1fd855beb9962a29e609654ba916352adf2e3))
* 摸牌动效优化 ([7e1120e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7e1120ef405388e398bf02c8b000713112c4f337))
* 摸牌界面的收起逻辑调整 ([7a0eb76](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7a0eb76a2bed5facc407728d2701cc5734e7e093))
* 摸牌时的交互动画优化 ([3406962](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3406962b95c4a84af8a4ca70e6d56ada728647bb))
* 摸牌时鲨鱼模型距离拉远 ([a71bbc5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a71bbc569fc3d2d7d56c576cdb1d8d48b4d23028))
* 配合iOS新webview调试背景音播放 ([0d9a829](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0d9a8296aec1d69cd9b234804d47c0293bb5bf49))
* 屏蔽麦位表情 ([f9a4760](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f9a4760e24ab0730b08c01cb3d71e1fe8fd6d743))
* 其他玩家出牌 ([d93fac7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d93fac7c9bd47868f5148347101d21d35a5309c6))
* 其他玩家摸牌 ([a832503](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a832503d1a619455cdcc5d803b82e2af8341be90))
* 钳子按钮调整 ([2f38247](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2f38247d38b8fbf7b93703dd4741efb380e5abf7))
* 强化动效 ([ff76555](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ff76555d3f1db672827d0f6716b4f09827730aef))
* 强化后剩余刀孔不足谈提示 ([802df2b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/802df2bfc36eebc0f70f8ef48ccc456e8eec97a4))
* 切后台播放背景音测试 ([5b8eb3c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5b8eb3cbae0e45f7c1d4637102e5a49718ee36a2))
* 切后台不断连 ([bf7ac49](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bf7ac4953ea769ba8ef140ca06bd7803f08d84fd))
* 切换 sgc 依赖 ([9d7ac14](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9d7ac146a941e976047cc4c4134b33a436da4962))
* 去掉 loading 场景 ([afffce2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/afffce2f1a77ad31868d931532656d7801681241))
* 去掉天空盒 ([0fbd1f6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0fbd1f608701bfdf824cc912047b03cf5064c435))
* 去掉牙齿阴影，减少drawcall ([8e6ea71](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e6ea710d0be4a8267c4f7e4d6be2636940637ea))
* 去掉牙齿组件 instance ([f85e3f9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f85e3f91b07816bdfb49cf7cca48e59c31e1181f))
* 日志优化 ([f4a68e4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f4a68e47e6605a458f1f3376aa9cf45fe61b8b3b))
* 鲨鱼触摸旋转优化 ([fb112a3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fb112a30f297d02c343af845cf20309b7ab90251))
* 鲨鱼触摸旋转优化 ([9c7ecd3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9c7ecd30356a3c6a24b230302f094ab7033181eb))
* 鲨鱼触摸旋转优化 ([dc11c93](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dc11c93b404c77fc625f79d88da34e69dfd85141))
* 鲨鱼位置调整 ([903022b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/903022b0e567f82c57ba7f2915491440e9590d55))
* 鲨鱼旋转优化 ([5ca378f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5ca378f63acf21bea7414da1e6e3bf115d2534cd))
* 上报错误优化 ([e85b75a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e85b75a96ea4b08e19058d978fc46800df42a3f9))
* 上报错误优化 ([ba4ed1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ba4ed1faadf7357f3a3200757ec955ab01adb0d8))
* 剩余牙齿数量不足的场景下诅咒按牙的处理 ([db25f41](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/db25f4161dc338ef093b037a508d80cf774cc8ae))
* 实现测试和联调环境日志导出功能 ([b0aa464](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b0aa4641b7953dadec337180d311da3b844d8057))
* 实现测试和联调环境日志导出功能 ([c1b93a3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c1b93a3a8ff83fbf3cab57bf2232b668a353a441))
* 手牌点击交互 ([57297d5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/57297d5fc9c71459dd208d2f4e661e94f97e50b3))
* 手牌对接 ([3ecd066](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3ecd0666a7ec4aa445bd1c330039c9c8e3ef1f27))
* 手牌区状态 ([8dd8493](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8dd84931c35fbff7239065c849451bfbf8f55079))
* 所有游戏同步性能优化改动 ([e089d40](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e089d40cef2c2f315043995446637fdedb5d99ff))
* 索取-头像上的动画 ([f719785](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f719785c3877012998f99bee64cd751910a4b99d))
* 索取按钮替换 ([9f6aeb1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9f6aeb173f703d072a3949a88816705399305dd3))
* 索取动画缩小 ([f74f0ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f74f0ae8e4822ea6be4c08f6ebcd0e0a43e98ee3))
* 索取和休想动画层级调整 ([128ef2c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/128ef2c187f9a87f78156a8d8b8ffbb91edbf8b5))
* 索取时增加动效 ([5878396](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/58783963bc4bd33ed873e5fdeff3f6035659502a))
* 锁定20帧率 ([a0f517c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a0f517c4fe7512dbb5c551d23658bd49a784cb20))
* 逃生界面的布局调整 ([5c1bdb7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5c1bdb7893b3306b3713f381b871a44b8351fa5e))
* 逃生界面的布局调整 ([8be3d28](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8be3d28d793bbafd1763594421ce1e1bf2381e29))
* 逃生界面调整 ([63c792e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/63c792e5d8cb200ba4ad41031cbe5d540d2bd9c5))
* 替换酒桶贴图资源 ([334fddf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/334fddf7fca582615be7dc12453400ca4ba448d0))
* 替身动画 ([3c147b9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3c147b9b7b50bb801d7598a151d8e9ad643b5991))
* 替身激活无法选人时提示 ([409011e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/409011e3996475f7cad74d19cd2366a3686fec6d))
* 替身激活选人 ([86a76ea](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/86a76ea042d4639e329d547f117916aef1bfa757))
* 替身选人 ([f13bdf8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f13bdf8c7bd13f3c6ff754ca5317c4f6db08654c))
* 通过url传 params的模式在进入游戏后直接加入战斗 ([f890595](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f890595dca9d9227964177987e5a90c20be2dd0e))
* 同步 shark-suileyoo 分支代码 ([4f5ac9c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4f5ac9cd0b9dc7506c8e88c31436e3c60b2fd00c))
* 同步海盗桶core到 shared ([628cb1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/628cb1f47571ca559b0de4a802e9ffd0dea695ab))
* 同步框架 ([5d5cbdd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d5cbdd4fdb5138143b9bbdd77569d4a0784be6c))
* 同步小心鲨手core的调整到海盗桶中 ([e12ebf6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e12ebf62f47ca48e7eded870c286b17c8764d492))
* 同步装扮框架代码 & 海盗桶对接装扮 ([81eae5a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/81eae5ab8bfb35c7a2f146f2862e02480904fd34))
* 同步装扮框架代码 & 海盗桶对接装扮 ([04fa121](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/04fa1215385933a41f67db2f495ef5245808e2a3))
* 同步装扮框架代码 & 海盗桶对接装扮 ([cfd5e61](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cfd5e614be3413f118703e5c8e89eae2e2f5e21e))
* 同步装扮框架代码 & 海盗桶对接装扮 ([29df5e6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/29df5e6ffb972bb0173724ba5147629d3911b90c))
* 同步装扮框架代码 & 海盗桶对接装扮 ([4b4fee6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b4fee63e83fa5204a0984b27d6066fac3e157a4))
* 同步core到只言片语 ([663c2e8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/663c2e85dc70d7ccb80c6eef02c084e1d30487b2))
* 同步shared 到小心鲨手 ([463205b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/463205b87ca31004da4907609dc785705432eac4))
* 桶转人不转 ([b217a6c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b217a6c1de19b9ae452a2382567829c6254628d0))
* 头像冻结动画缩小 ([db504e1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/db504e1acd744188fb16d8a21097f1b89ef5b365))
* 头像对接强化状态UI ([2af0569](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2af056981f8679bd649b24e9f8243b1cbca3c4c1))
* 头像对接强化状态UI ([186f5dc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/186f5dc183d9d813b04a30905cb6b447d10b0a37))
* 头像对接强化状态UI ([3ce8398](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3ce839835dbcbb3aa683b6c3444cd87f31c2ed33))
* 头像对接装扮 ([745014a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/745014a104fd4a4cb579b38b0e416497b15af5e5))
* 头像对接装扮 ([4b19f99](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b19f994ddf1e750beed6f73b8b586311a86dded))
* 头像对接装扮 ([3bb8d07](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3bb8d0712d151544a695adfde184a8fa86700e6b))
* 头像对接装扮 ([df6a931](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/df6a931f69cdd197a4e847626d06f84e21d83896))
* 头像延时状态和虚弱生效动效 ([24e678d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/24e678d5d221284d5b0a0caa868ea99f33742203))
* 头像延时状态和虚弱生效动效 ([03a16d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/03a16d1920a4ce83add15cebbbde0b96fb8ce586))
* 头像延时状态和虚弱生效动效 ([476c5a1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/476c5a18dd6a3d1d4fbe5ab9fe74c090268f2946))
* 头像增加弃权 ([9e34ebd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9e34ebd96ec579e78cbb0a2bd4aa2125e4a2dbc1))
* 头像UI ([9622311](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9622311ee9d3f4de2b5ee8269357e097cd913502))
* 投票和玩家标签相关调整 ([03c0e7d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/03c0e7de017d4174fab1a3b2ee4864e27e639ce7))
* 投票和玩家标签相关调整 ([e80a463](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e80a463bae376fc6933cc7d97dc548682e6c4eb2))
* 投票相关调整 ([8639dfb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8639dfbcd8d2e9f1e06125dc4d750cc3bd230ff5))
* 投票预制件 ([c4b1fff](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4b1fff5b27f162510567cda8283803209034c65))
* 投票增加卡牌选中状态 ([7092ae0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7092ae0c137bee3b0cff1eff1752080e00424d01))
* 透视 ([5e3b7bc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5e3b7bcceb85330fae4519f3e0eae842d9b7f1d2))
* 透视动效 ([79cf8de](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/79cf8de575891c1b16ec1f2bec99a649a4c1522f))
* 透视流程调整-角度旋转优化 ([92c3855](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/92c3855ad11b5d2d342cb4666ba0a98ac455dd87))
* 玩家头像 ([85a65a8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/85a65a8388778e2a441366f34db10ec249dab669))
* 玩家头像 ([2b692a5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2b692a5acbf342eafee7f951d1becbe2ebe2c145))
* 玩家头像调整 ([f79ec5f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f79ec5ffa182a2da9f0b3901783b29ebc907f10c))
* 玩家位置分布优化 ([95c441e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/95c441e3c7cd7db21afa4b15b4b2d4cd0962a32a))
* 显示操作按钮的逻辑调整 ([178ad30](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/178ad3053ccfd5f45462cc544da667f7168f8980))
* 显示积分的机制 ([010c860](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/010c860ab5cac0b132d6d070d028bbf29806c389))
* 显示积分的机制 ([4d70e57](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4d70e575118ab6930f2da6c2a86ea8a0c5740f76))
* 线上服务配置 ([0a706e8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0a706e8c467fd6c319c61f22172551d58a603a2e))
* 线上环境支持 debug=1 开启导出日志模式 ([498aee1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/498aee1327984ea271e9bdbd11adc0849709c9f4))
* 休想动画 ([6d5f08f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6d5f08fdf546594ba6f02a42bd04da153cc241ae))
* 修复观战路由 ([8003f3a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8003f3a54ddd8d518065db57a7e25c65cae23333))
* 修复mockSharkRotate ([3c02b1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3c02b1f827b9064b9f9bc85ade404976912f86cb))
* 旋转实现调整 ([2b44944](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2b4494480aebff9dbdb9baef0d5729259c5c210b))
* 旋转实现调整 ([267bef3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/267bef37d049042253fbda3aa687fd53b919dc6d))
* 旋转实现调整 ([7af7e6d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7af7e6de7a6c79a16be31bb3e3f8ac2ca9a4a3bf))
* 选牌交换优化 ([bcf788e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bcf788e48b55ecd5bdbb86b2a8482e44379ce7cb))
* 选牌交换优化 ([1cf0e37](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1cf0e3709907b36c57e31d247cfc921d25899686))
* 选人调整 ([c0a487d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c0a487d54d511fb4f189830f40a9b52a4fa9c311))
* 选人调整 ([cbda7f3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cbda7f38b560f7e37f75e404db208d5c4418bc38))
* 选人调整 ([c2c31df](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c2c31df081285b983d549f7c3f35ea9aa434f69a))
* 选人时可选列表根据服务端返回计算 ([3cbc04e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3cbc04efd9e0ba9e69413d0c48d4bddbe3b97faa))
* 选人指向调整 ([81b43eb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/81b43eb0b9db733c522165b36230b24e309a1533))
* 选人指向调整 ([df45a2f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/df45a2f20fdab381a5f827001fabb5df42502fd3))
* 选中卡牌后的提示组件位置调整 ([e485779](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e485779d3e8c7ae40bce8de3a6a16cb173548c8b))
* 眩晕特效挂点海盗头顶 ([c5c5aa7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c5c5aa784572269074ca70ad2bc67b6adcf6f4a8))
* 牙齿点击增加防抖 ([dddd3e4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dddd3e4a2584ddf16755bb8879290fc31063865f))
* 牙齿点击增加防抖 ([ddecd67](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ddecd675ea557dc754d5259cb93d1c9f71d21961))
* 业务对接新的 proto ([f756f98](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f756f98cc9a6ea81ebb22ad31af57c144fa29e6f))
* 移除无用的节点和贴图 ([b0a684f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b0a684f2a8313d0cfb6467ab863e8fae596fc8b1))
* 已经按下去的牙销毁碰撞检测组件 ([f3ab9be](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f3ab9bec44e4efac334e302e0db02cea01f8ca03))
* 用户标号改为颜色标签 & 抽取预制体 ([09b95cb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/09b95cbc9b8c77627250dd94b03f064e64b5fbfe))
* 用户标号改为颜色标签 & 抽取预制体 ([33e6745](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/33e6745ecf4e5faf223c9632f4c8a6f55f5bca5b))
* 用户头像标识调整 ([af3f2e8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/af3f2e8d80d15367528b33c7788280e4b630e32c))
* 用户头像标识调整 ([a4f758a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a4f758a78714d5a651acd47f2365cd0f90db518d))
* 用户头像装扮 ([998167c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/998167c44179e615d6ec98fb22c5ca88f047dad0))
* 用户头像装扮 ([17cd0c0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/17cd0c0411e7547bb70486279937c1deb50568cd))
* 用户头像装扮 ([036f82a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/036f82a05857207f5c45b8cf39a4e9c9925f9f17))
* 用户头像装扮 ([4fcf99b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4fcf99be461e48e8162eb1c3f7b2a9f741b673b9))
* 用户头像装扮 ([1c2cfc4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1c2cfc45bc1773c8aece6030ce012cf914001117))
* 用户头像装扮 ([259a067](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/259a06766c50592530846ff0cae43fc8741b6622))
* 用户头像装扮 ([a50c40f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a50c40f41e0132ab4b25acff044ada41a68c359c))
* 用户头像装扮 ([309de3d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/309de3d1b0f4cbe8283a305ef2cf3cafa2db1f34))
* 用户头像装扮 ([ac23aa5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ac23aa5bc082866c8d3e2dcb57402215649ab135))
* 用户头像装扮 ([8a429b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a429b4f75c92ecb70049fbd1aa727fa8d500cae))
* 优化超时摸牌相关交互 ([2e1a161](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2e1a16133e8060eaac553204b22561148c336992))
* 优化摸牌相关交互 ([f78b788](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f78b78871408b7016276203d286ac6bbe1e5b758))
* 优化牙齿碰撞体 ([b69437f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b69437f2ab8a6e2c76b3a846d9925c985b65fcd7))
* 优化noMask没有弹窗放大动画的问题 ([8e63bde](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e63bdeb75b27eb3aace4f401f4dd963b9674cca))
* 游戏背景 ([8f841ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8f841ae57f2812e28d45bdbb7e0ad5518dfd5ade))
* 游戏初始化 ([afd1e04](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/afd1e0450a4aaa8e67019a8b3179576dd2cb3f86))
* 游戏的开始暂停 ([4cb142d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4cb142d2ba49c58c299b3a14eec803ce2550ae5e))
* 增加 LogMethod 装饰器，方便调试 ([e7689ea](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e7689eaabc9cce119e7b8d89e06b77a70227608a))
* 增加击中非弱点的2d动画 ([a1dd59f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a1dd59f3715ff8da9c72e589f8ae369578a51f2d))
* 增加讲故事弹框预制件 ([6927656](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/692765641ce6e21f9df984562000f1ca79a8ed9b))
* 增加桥接方法同步到各个项目 ([42600b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/42600b42075b1cb4e71442fb06d389f8c45df2d6))
* 增加鲨鱼咬人动画 ([4153a17](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4153a17dbe7a982bdd3365e27f5f5e547990afd4))
* 增加新的牌桌状态 DESK_STATE_UNSPECIFIED ([2098265](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2098265db5dc35f4fde91e50e9379ac73329b3f0))
* 增加新的jsbridge方法 ([1b32549](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1b32549ee4f8572bd9f24679cfaf879d1c0d9f67))
* 增加遮罩层不透明度 ([712973d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/712973d0ce2642dc272533b875ff0365b7a00c88))
* 针对channel=web，对局结束后返回上个页面 ([b37e6ca](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b37e6cac0e8ddadc519f68f0cf76fa2f3a30e21a))
* 针对channel=web，对局结束后返回上个页面 ([ffe4fe1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ffe4fe136b7366fb112131f323a65956dd5af3a7))
* 支持根据url传参区分env ([edcbc1c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/edcbc1c74025763e60dd8406b69bc4b1c925b80e))
* 支持更多的强化次数 ([ba5f809](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ba5f80995c6581bfd979a1b746686f8f01ee9f7c))
* 支持更多的强化次数 ([6bcecb5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6bcecb553b3ca849a11db37e54ef3c657f3560a4))
* 支持更多的强化次数 ([a4d6f19](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a4d6f19d8487bcfac8136bb39e335d657f5beed1))
* 指定默认场景 ([60fe4ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/60fe4ae713cb5c8d37eb368e914e3c994cef65e0))
* 中诅咒时玩家头像状态调整 ([bb0f806](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bb0f806aca5afdd16a29f855bc2457bd8e57f6f4))
* 中诅咒时玩家头像状态调整 ([87deb4d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/87deb4d84d41f4fd827686d0b157a4f96d3e3671))
* 重写海盗桶放大缩小的逻辑 ([1196f98](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1196f9823113d71569aef604758d2b51040de1d2))
* 重新对象管理池 ([43461d6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/43461d669b45cf697aebfada079d2865669d6c8f))
* 注释调试代码 ([2a7b45d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2a7b45da79c09c64d5857619474523039a83bcc3))
* 祝福 to 祈祷 ([7d52026](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7d520269b85e26eb72df2ead7135f50b2d4653cf))
* 祝福交互优化 ([b7a03bc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b7a03bc86dbea4a8c4aa7896e1bdc7dd733755c3))
* 祝福交互优化 ([f2414db](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f2414db2335d71b8f2474bdaa01b7882d4aac50c))
* 祝福钳子对接美术资源 ([0430d7d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0430d7ddb70127b9a98caf73045031ba4b08f279))
* 状态变更消息增加时间戳检测机制 ([aa4fd01](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/aa4fd010904523b0f7c0949c30cb0e7285617a01))
* 自动图集 ([fa48fe4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa48fe4a951676c3de05c7ec7a6133a1bf883e28))
* 诅咒动画对接调整 ([4c321a4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4c321a4ffc6db7e3d1eda4d911bce19d41ed6dcc))
* 诅咒和休想音效 ([a855c56](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a855c569129e59b29eca3ebbb63e4907133bd19a))
* 诅咒摸多张牌时，不收起摸牌鲨鱼界面 ([32816f5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/32816f55171637544b265043700a34b2945268a2))
* 诅咒状态下按牙选中牙齿数量不足提示文案优化 ([1a51fb1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1a51fb1f8ef6209ee97e09d793e8fa314618eea5))
* 组件的处理 ([6f60c94](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6f60c94daa5742ea6399624d79f6efe1ff6e8884))
* 组件的处理 ([fda3e27](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fda3e2711185a7497ef6011bd342458443833995))
* 组件的处理 ([72d47a0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/72d47a0904670f467125f440145310ffffa15a66))
* 组件的处理 ([9947473](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/99474735e961fd5009ff6188f2c73f35502fec5b))
* 组件的处理 ([f63ee97](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f63ee97dc3cddab60092a89e7bb6ba01ab28a393))
* add log ([be0fa68](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/be0fa68e4301ff41d16885db9dfd5eec729459f3))
* add log ([832819e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/832819e4d18624c38b327c40d31b1361de349e29))
* AI 模拟玩家索取选牌 ([b18fec5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b18fec590547b8a373115f2f0d775a3459803bde))
* AI 模拟玩家旋转鲨鱼选牙齿 ([a7cd000](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a7cd000442b125ce133376807217420a566744ec))
* AI 模拟玩家旋转鲨鱼选牙齿 ([3f35911](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3f3591136bd8b142e2ba3e31a2c832c8256f5707))
* AI 模拟玩家诊断时旋转鲨鱼选牙齿 ([22bab65](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/22bab656208e0b0cb03b9d0326b9981650fdb693))
* audio 相关调整 ([cd1b714](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cd1b7144b258f1ff82dcfca443ac613edbd90831))
* bomb-duck 项目pb ([320193c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/320193c1c4c221f5da21165d479e882b3493a8c7))
* build config ([6cdcf06](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6cdcf0697ad9b0704dcf091791cd9f800ada57ab))
* build config ([2523a51](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2523a51ae1749f6558571e8dd9b339ff2d299791))
* cardInfo 交互优化 ([e5f133c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e5f133cd361f33e890e0ddb26cd0bc30e90b9f01))
* cardInfo 位置 ([d917780](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d917780832e17b9756dcbd76750e980f1cfee44b))
* CommonDialog 在关闭时增加动画效果 ([ecbfa82](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ecbfa828c8e550da216f15f89ad3f19aea3bff9b))
* debugMode 也通过参数控制 ([d7cd8ef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d7cd8efb0a45a278edf4467afa0dd1d9dc0d7483))
* enableWebDebugger ([8ab3933](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8ab3933965d194037659d49ab59a6a14a9f83a24))
* enableWebDebugger false ([7dfdb4a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7dfdb4a816753c8b213f479fe0ddbb325eee1241))
* EVENT_RANKING 的处理 ([c34b0fc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c34b0fcd4142e4b0960e178cf91659dadc1619cf))
* flutter 平台使用 dsbridge 实现桥接层 ([97c9a23](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/97c9a230cd7652c9afa5669b3dcfd43314b736c4))
* game 场景搭建 ([c55fc4d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c55fc4d2086ce31dbd7378182a44889e9f32a1fb))
* game 场景搭建 ([804ab67](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/804ab67ae6b3a1eabf5347fae4e4328ce5ad39cd))
* game 场景搭建 ([1083645](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1083645695fb6dd7948d56b64177d010307224ba))
* game scene ([06915f3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/06915f31238c9e1ac716e3f341a90e116a0533f7))
* gameUIMain prefab ([f4196f3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f4196f37a87ff949811e5963502dec1d800f60c4))
* initTooth 调用 ([33fb9f3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/33fb9f3c13041f0b3a8d40177b7693367c282c26))
* initTooth 调用 ([2a86e48](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2a86e489fb33b0f7e314e36d87e4a66210caaf3d))
* initTooth 调用 ([91622f2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/91622f2765ff14537520e6d286f3df79dbb3e608))
* JSON.safeStringify ([66b937b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/66b937b951e50bd23a73adb6171a7cea4c208ae3))
* loading 和 start 场景 ([9fc4626](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9fc4626c1afe90cab9af84fde577f06af4ffe708))
* log 上传机制 ([48d736c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/48d736c87e83bdfdfea8866715d6749d483c0cb6))
* log 上传机制 ([782f26e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/782f26e054dd0ecfd7ad6c8f8a56940d0fa4b67e))
* log 上传机制 ([3f9bfe3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3f9bfe3f7d5fbfc3ade4a31582a04e8f2c269b27))
* main_title 层级问题 ([5d14315](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d14315bd4fd176476a4d7df3a4fd04a30d48950))
* merge json ([e1f55a7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e1f55a749d85b43207ba829fad94b1d158be93c5))
* merge json ([4ce71a7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4ce71a7438d1e0a04c8dac7bf7d69a880db9aa32))
* onActionDrawHandler ([42f8717](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/42f871740d57b7e925c029ccbd3db77a6df5d325))
* platform 为 flutter 时不显示游戏内小结算界面 ([0138fcd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0138fcd6e98f2d4e27a22d399b1d19ef6a39857c))
* player_item 调整 ([2e2883e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2e2883e0bdd1c09b7308304c0ed4a4fd453ebb86))
* playerItem 简单对接 ([168f4d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/168f4d1d1ed7470183e0ecda51edbab8f7851e1f))
* playerItem 简单对接 ([845c732](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/845c7320f125a070a7d373a9ad1c019954ca77aa))
* playerItem 简单对接 ([8313ae8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8313ae80af77ef2f692dcc538c68fa99016b79f5))
* plistImage 支持添加 .plist 文件资源 ([8cec5b3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8cec5b327446582a4ed307e1060e17f35903ce9b))
* plistImage 组件支持播放本地plist资源，支持设置播放完成回调 ([146b25f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/146b25f86afc630d1a189bd7f352ac5ef04c890c))
* rpc 方法支持传入options ([44ece8c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/44ece8cb89c9217eba18dbba508b1f20354b6fa4))
* sgc 观战相关调整 ([de1f905](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/de1f90515c86ab859f21e61df845a8daff1f83e9))
* shark touchmove 还原 ([42e8ff1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/42e8ff16eaff69b84d1894342147f2e64fa5cba6))
* store.game.playerList 整合 Player 和 BasePlayer ([9e793cd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9e793cd0472e42bd9c84d77ec5d895562dd73cd6))
* store.game.playerList 整合 Player 和 BasePlayer ([b919a4f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b919a4fdb6404240893b1dde07c0fe74ab3455e5))
* store.game.playerList 整合 Player 和 BasePlayer ([096f92b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/096f92b91343d83c68dd87c59431557f0f5b9189))
* toast UI & 唱票所有玩家弃权处理 ([8d94d33](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d94d3377cf7c8a2da8a86c4656d97fd66df6b91))
* UI 调整 ([4e29a92](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4e29a92b184b8791bfb93e060784ecdd8115be5b))
* UI 调整 - 摸牌 ([1c858d5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1c858d55237f6d4541aada01bc86febb1ce41588))
* UI 调整 - 透视 ([b6bdf85](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b6bdf8565b71910fa42c547a212aaa8ce6d4ee7b))
* update pb ([35c2225](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/35c222518d517e5e39c8e895b608ac36e6081442))
* update pb ([c7deb86](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c7deb865b5725d3d9e7d24a3219f375740d4229e))
* upgrade pb ([596d168](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/596d168f16d9c9956cf7485869013858d7dc97c5))
* vote ([b665eff](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b665eff84ff35da5de80200830996d2f61ad8888))
* W揭示说书人动效：魔法棒+翻牌 ([d092b7e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d092b7e5e87eb24c070146d60d916e8563c1c6db))
* WIP: 唱票 ([3ea0a92](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3ea0a92271afe2b512e6e3372616430f9d9345fc))
* WIP: 唱票 ([ad25d7d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ad25d7d52db0ebba09d4d4efb7b1fc6217ac1216))
* WIP: 唱票 ([5d1b254](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d1b25441930ed145628a81a7cdf8f7355c9fe27))
* WIP: 唱票 ([fc74b9a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fc74b9a58753a7444f49d7bf6d2cc1b09374eee5))
* WIP: 唱票 ([86af428](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/86af4282287a39e0bd79ddeba5009e3bbad3f1f0))
* WIP: 唱票 ([c0b2b81](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c0b2b81b2b94873621100307367313ab20027723))
* WIP: 唱票 ([9e6b8ec](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9e6b8ec07f4bf9d60ff00976c7c71cb890c11703))
* WIP: 唱票 ([9855b27](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9855b27949fa504d224a71d3ea24d0d2ccbc58a8))
* WIP: 唱票 ([e28f1e6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e28f1e6387b5554e566c1eb19b0f47653d22c31d))
* WIP: 唱票 ([7638cbc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7638cbcea014242d937699a3f3188b67bcc1c336))
* WIP: 唱票 ([df03b11](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/df03b11af204d382ca6553bc28f0ba0b75f22d0e))
* WIP: 唱票 ([b5df69f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b5df69f9a6134070e870f46760acfc53cad6982f))
* WIP: 唱票 ([bc8b412](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bc8b412bd0b70cdc8e2ad9c5de1934c5c095dd2c))
* WIP: 唱票 ([c5291f8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c5291f82c881f437d43ed585666f3ad7448850f3))
* WIP: 唱票 ([abdec24](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/abdec24da8bfe1c96e7c79b07df1e814f1a94fcc))
* WIP: 唱票 ([9ba777b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9ba777bd7ce33dc66be82494b332708e94044017))
* WIP: 唱票 ([cbb1c5d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cbb1c5d855834b09c42a91d916d74206be893356))
* WIP: 唱票 & openUI 支持 noMask 参数 ([075cb8c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/075cb8cead3daaf2108f3ed2be90a920f6bc4434))
* WIP: 唱票显示玩家标签 ([6e33b77](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6e33b77994a29a448caadc57b139317b32dfa46f))
* WIP: 搭建核心玩法 ([f67cc3c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f67cc3cc9111cbe550e4026a968a89d0c4529bfe))
* WIP: 搭建核心玩法 ([e2b311a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e2b311a8d344eb4b8f1193920d5c9a5fac249085))
* WIP: 搭建核心玩法 ([b02ebcc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b02ebcc8bfce317efc307b7e4c8e90cfd3bc0b74))
* WIP: 搭建核心玩法 ([080cd4c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/080cd4cf944ae38fed646dba9a0ca95b154ac105))
* WIP: 搭建核心玩法 ([bfcce46](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bfcce469df41229f7415b19fdec5efec27b85bba))
* WIP: 搭建核心玩法 ([35e9ffb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/35e9ffb8dc43a750ba1d0af375923786564d6cc3))
* WIP: 搭建核心玩法 ([b19ae8a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b19ae8abb15de39bdb13e3d3e33fca3dc0011cf3))
* WIP: 搭建核心玩法 ([385acf8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/385acf84502aab4fd837429b798bf9b1507cee10))
* WIP: 搭建核心玩法 ([8e0db1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e0db1fc3db4a37a4ba89afde509b29ee0ebb222))
* WIP: 搭建核心玩法 ([e8aa38e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e8aa38e417ae62360bcb893b567b70e4c02f2793))
* WIP: 当前玩家出牌 ([8a9deef](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a9deef428b840216f2a0dc6811cfa48e70b0104))
* WIP: 当前玩家出牌 ([40f902a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/40f902a20f269426f10b9de37ad97ea6e6609afd))
* WIP: 动效 ([6973917](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/69739170802c168addd20de8b373cff5136cff61))
* WIP: 动效-冻结 ([23c1553](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/23c1553406bdec1de8deff1eb326f503cb72365b))
* WIP: 动效-冻结 ([4116483](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4116483650486dd21b614dab6bd53a976014271b))
* WIP: 冻结 ([1cf3561](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1cf356154bd720d846440a0ce3a221b3829fb726))
* WIP: 防御 ([70ab796](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/70ab79602e41f7dd84f39aeb257ea13f63197246))
* WIP: 攻击到弱点 ([7ccb78d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7ccb78d22d246eb901c4b8b58fcb0cd37f5292bc))
* WIP: 攻击到弱点后的流程 ([84c27a3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/84c27a3d7a429904f0eac418771d93e78c8af22d))
* WIP: 攻击到弱点后的流程 ([e4681ae](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e4681ae6ff964a9bfd94d797ecec96ab8db01afc))
* WIP: 攻击到弱点后的流程 ([9c06b5c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9c06b5c4a9eb74c13f5d12005d8c903bb6300267))
* WIP: 故事书 ([f3c3eba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f3c3ebab2486eaa52c8969e285282a9ccc81ba5d))
* WIP: 故事书 ([8df1c29](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8df1c296ef8ad1c825b8282790bd659accbdedfd))
* WIP: 故事书 ([78b5453](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/78b5453fe22a46b0bfe3557441da86dc7cd7dbdf))
* WIP: 故事书 ([6feab5e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6feab5e1ead139d060850ec1b415423e4a9450e8))
* WIP: 故事书 ([3757dab](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3757dabeb7db95bcdd5ba8e52c58c735ce00b837))
* WIP: 故事书 ([91da51f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/91da51fe85b1b18cb3c532eed0fc7fa188bf3549))
* WIP: 故事书 ([aee805f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/aee805f119ee4ea3e3a259e9dbc864c6d9bc77c8))
* WIP: 故事书 ([bffc1fa](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bffc1fa9e67893f23dac2d8b96a74f84ac28554b))
* WIP: 故事书 ([78d0803](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/78d08038cb9742a50ca42b0872060974de02afdb))
* WIP: 故事书 ([9710277](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9710277f85e833f1bd44d36b0704eb7e5b2bc0cf))
* WIP: 故事书 ([fec730a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fec730a53a458ce927aa09396a12ab436780284a))
* WIP: 故事书 ([de70e91](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/de70e912069250c365381ebf8b31627815c8a155))
* WIP: 故事书 ([e2cbf1c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e2cbf1c5aa942a28fa41813af1c932e44484e05d))
* WIP: 故事书 ([bd89887](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bd898874914418be9b2a031dea0e5e08a362b805))
* WIP: 故事书-轮播切换优化 ([bfdc1c5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bfdc1c518b6604ad042aa64ca8b163dbe1178c25))
* WIP: 故事书-轮播切换优化 ([1aa95fa](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1aa95faf91b483182267cabfaae6b037d26d252c))
* WIP: 故事书-使用proto中的类型定义 ([f8e158d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f8e158d5432b5e6697d94a7c128b17e1e290d054))
* WIP: 海盗桶放大旋转 ([de213ff](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/de213ffafe81c87cb47de79ed96963048b5e5cc9))
* WIP: 海盗桶放大旋转 ([17a3cbe](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/17a3cbebf88791459a397426322462a71d34727d))
* WIP: 海盗桶放大旋转 ([8ab16b5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8ab16b50aedfaf9ac8c970d9aafc6801287b6e80))
* WIP: 机器人对接 ([27303b5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/27303b5f9c4ba7f5484e50a7c5a082592fccddb4))
* WIP: 机器人对接 ([f9bf866](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f9bf8667edcf8d759a06b74f5324786f3b80a60e))
* WIP: 机器人对接 ([26c6d2a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/26c6d2a4890e2cfcf50ef4b85e88f25f77b19999))
* WIP: 机器人对接 ([41da1c8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/41da1c853bcf3796cc84c6a00f65d57b1fa2990c))
* WIP: 交换 ([fe773b5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fe773b5bfb26ef1c5c5faefabe14fdfae4a9f148))
* WIP: 交换 ([9b008d9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9b008d97a5c33e2b7c93ea7448ec844188f7b903))
* WIP: 交换 ([04deb8b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/04deb8b9dd986e66183197d8b5263cd9bbc3ac3d))
* WIP: 交换 & 选人后动画 ([4072a81](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4072a81de6618af64022b726ce03f9736ab206a9))
* WIP: 卡牌 ([a8f82f7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a8f82f79e278ed3174553f88f5015514c0a2ac6e))
* WIP: 卡牌支持显示投票图章 ([865de5b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/865de5b3f9946fda22d741cd8a29b2bf92955e9a))
* WIP: 卡牌支持显示投票图章 ([5c0d182](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5c0d182109ba48d63ec085e75b2221c344e40509))
* WIP: 卡牌资源动态加载 ([5023b12](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5023b127f24251e6d5c849de6965e844e5e4c2df))
* WIP: 卡牌资源动态加载 ([670d2c6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/670d2c6102f5f859573ad3a07a6b34f783e415fd))
* WIP: 卡牌资源动态加载 ([f7c9472](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f7c9472182a66556f40da36674c99accbbb86f9b))
* WIP: 卡牌资源动态加载 ([c09cee2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c09cee2cd9ccf41486ba0fc4f1ff1789f9ae16cd))
* WIP: 卡牌资源动态加载 ([07066bc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/07066bcbf153f1efd52ba6e0881e07d6b2e6b93e))
* WIP: 卡牌资源动态加载 ([67d90a6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/67d90a64cc672636b95756aedb8bf7b9f9f8ebfa))
* WIP: 摸牌进入手牌动效优化 ([2e7efcc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2e7efcc6cc574cd7ad2e1198203a1337fe11933e))
* WIP: 强化 ([4f6a847](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4f6a847a080d7d282db7498787358bad57b32f71))
* WIP: 强化 ([a69512c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a69512c1ef501fc267a7725318984e47b7504503))
* WIP: 强化 ([6dcdbbd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6dcdbbd2cd68cbacd27d34eaadcd57e126ce5b71))
* WIP: 强化后攻击的流程 ([5dfdac9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5dfdac92877e5298d0a75381283bef294942b7af))
* WIP: 手牌同步的机制 ([0ce45a0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0ce45a04b02fe5ed6c19a4d0f342d56bb09fa2b3))
* WIP: 手牌同步的机制 ([1d4292a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1d4292a5f39a8373ab64b7ca618fb37f2b8c77cb))
* WIP: 手牌同步的机制 ([9a1dd4e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9a1dd4e39564c210bfff339014ea92d429a4d5e7))
* WIP: 索取 ([4ac746c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4ac746cc02bd6ad7c2828705b47435c5764f5727))
* WIP: 索取 ([bc68f9b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bc68f9bb2c1d1e5b222bf227109f368bdbef4fdd))
* WIP: 索取 ([ad4d11d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ad4d11d251ee8e047d6730506c7f74546e48e84c))
* WIP: 索取 ([4deb3c6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4deb3c653e63ca9758cd9352297beeea4fb3bc25))
* WIP： 替身选人 ([75ac8f9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/75ac8f9946c44378d316d093fefac06a3c88a88a))
* WIP: 头像增加冻结后的状态 ([af96ae8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/af96ae8bdf8940b82a1447da9a37d22121b2ce94))
* WIP: 头像增加强化后的状态 ([16de55f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/16de55f5b89798f3cd476f7b1c5756431b5f4a8d))
* WIP: 投票 ([e214acd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e214acd9808f30b9fccd690d262010af5b23bfa1))
* WIP: 投票 ([a54eb24](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a54eb2459b12ff7fed378ca8ee84d579500533de))
* WIP: 投票 ([bd9db14](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bd9db1456123cec118aaadf927e3439fee38c6f0))
* WIP: 投票 ([9eb90b2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9eb90b208c57b311e0a63afdcd1ae078ac363803))
* WIP: 投票 ([81c740d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/81c740d3b748eca29962a1ea5c694f3fef5dee27))
* WIP: 投票 ([0538216](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0538216aa9b4f7c5145b556ae371f79818c01a28))
* WIP: 投票 ([c35b8c6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c35b8c66c8512d0b5e8939184ca3725cae6b24c4))
* WIP: 透视流程调整 ([df05ff3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/df05ff3bcf87e9e739cf1a96976f78a317b145e4))
* WIP: 透视流程调整 ([13c7806](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/13c7806e59f9fabafc85505907ab2a82a7809b45))
* WIP: 透视流程调整 ([75b704e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/75b704e1b039b9493ce6150f4f0b7608fbf5beb7))
* WIP: 新增卡牌祝福、钳子 ([b9097ba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b9097ba39beb779ff1c4619b7364d1573850c41d))
* WIP: 新增卡牌祝福、钳子 ([43bd8ed](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/43bd8ed68dfc60bb6a52a17ee1a7d1d854de8b20))
* WIP: 新增卡牌祝福、钳子 ([9f10259](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9f102595ac0b65e92b944c49c61ddce0cb593287))
* WIP: 优化UI & 对接强化流程 ([1e1089f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1e1089fec98686b5c3c30574f8638142bf3b2209))
* WIP: 游戏结束的流程 ([ef9bb14](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ef9bb143c8a86aca12cb3739817e99c695c21206))
* WIP: 游戏结束的流程 ([c315226](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c315226f56d17fb4f570a5f33fadebe7ecceb3fa))
* WIP: 重新卡牌相关逻辑 ([d4ce1c3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d4ce1c309bfd553e66d4e941af0e2511c2fa9483))
* WIP: 祝福 & 钳子 ([8736d33](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8736d33a9c2e4eaadcfc58905e64f4bdce4b7fc1))
* WIP: 祝福 & 钳子 ([307c188](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/307c1880fbeaed606db47f8b82386725a5665e3d))
* WIP: 祝福 & 钳子 ([cf37c0b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cf37c0bcf6fd02daae73842f02d2dc514ae48d2b))
* WIP:核心玩法搭建 ([6f9af77](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6f9af77630547d44691456a1cbd8731ab5152df3))
* WIP:核心玩法搭建 ([95c7c47](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/95c7c47139aced1108a65dd9eb2facd896987e2e))
* WIP:核心玩法搭建 ([33ef37d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/33ef37d894c25a2b9d0dbc12cc6fcdf86293ba39))
* WIP:核心玩法搭建 ([0743b24](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0743b24b496edb238d5fe9e45a047b1df4699fc6))
* WIP:核心玩法搭建 ([8a8e88f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a8e88f3d753ff9ae1503633b86c923506fc1c12))
* WIP:交换 ([a30ad8e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a30ad8ea27fc9008c8bc39b8ad908001dc981b85))
* wkwebview ([76fce49](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/76fce49636a2ee3da3be0465c2329b6f32f3326a))
* wkwebview ([d12efaf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d12efaf71256cdbbef20916cda9ac03065980e27))


### 🐛 Bug修复

* 报错 ([4d24c0c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4d24c0cb19ef36f7fb8a08d47a8effb936d87d7f))
* 被动出牌后也收起之前选中的手牌 ([c4859d0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4859d02bc8757f7b2db81ed502780866958593b))
* 避免重复发牌 ([0cca9af](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0cca9afdccaeaa048b23fe04aa1df9d6ecddfedd))
* 尝试修复对局开始没有手牌 ([f847485](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f847485a0aa3b64dd74e681b7a000fd1607c1e96))
* 尝试修复对局开始没有手牌 ([fa23f08](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa23f0866095f770deff98e5c2dd9298cdbccf38))
* 尝试修复重开手牌为空的问题 ([b20bfc6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b20bfc6f263e9abd4c3a893497ac57120117fbb5))
* 尝试修复重连被踢出问题 ([5bc07e2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5bc07e2242724e43fe9e2402a30e287a8d5c5c22))
* 尝试修复装扮层级问题 ([06aacf6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/06aacf62ce29715a5cb69f82ebaccc23e7192475))
* 尝试修复装扮层级问题 ([b41dd4b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b41dd4b9f390928721b642bb62c91067dafb8b22))
* 尝试修复装扮层级问题 ([5ab7f84](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5ab7f8490eb45b81e5af3c231d0b52a6b0821c59))
* 尝试修复装扮层级问题 ([0766ca2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0766ca295f30aeb18fbf6927bacf2bbacbea45a2))
* 尝试修复装扮层级问题 ([d92ae1b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d92ae1bc2592d4adf499456d8acf021a44d18af0))
* 尝试修复装扮层级问题 ([034781f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/034781fabfdc66096a59954b359d4d28207371af))
* 尝试修复装扮层级问题 ([0dd31f3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0dd31f3ca35d8566a742addc473464933c1a1507))
* 尝试修复装扮层级问题 ([0fada1e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0fada1e8ae5a7d44cb20f8e5233c36576ed87d87))
* 尝试修复装扮层级问题 ([6d3401b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6d3401bb11e4ebc6eab83d2c91ab3d7e7df92ba0))
* 尝试修复装扮层级问题 ([fa1b7e4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa1b7e49124e9671477184dd6eba482025b67fdd))
* 刀孔分布 ([c034a82](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c034a82a815077251e348398da90394ad7525b4c))
* 倒计时 ([536cf1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/536cf1f2058a9f23cae5a83ef93538efd7b7eab2))
* 点选卡牌设置dobounce时间 ([fdf23a9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fdf23a90d7a1fc0d1cf1ef090487795ae2c1d72f))
* 对局结束时处于后台的特殊流程 ([3691bd1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3691bd109cffa92db578d554b1eb4614294757df))
* 对局结束时处于后台的特殊流程 ([04a9b7a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/04a9b7a9666ae0a889117fc516ebba81a327bf0c))
* 对局结束时清除store ([8a8723f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a8723fb5397d12035e18b2f6f9858a246fa7253))
* 发牌问题 ([9ea19e5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9ea19e535c25eba240296fe417730d9b63ee785f))
* 防止战局初始化执行多次导致的牙齿问题 ([efc8dd4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/efc8dd48198eaf3ac759eac53f789ed4e0458eea))
* 卡牌渲染时可能卡牌已经被销毁了 ([e94b7a9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e94b7a97d8a8a4c2a9708ee08ef805d06e06af5d))
* 开局时处于后台的处理流程 ([abde9af](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/abde9af842f3dec55be90cfe414b12e746698113))
* 轮到你了节点层级问题 ([9f6fc48](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9f6fc4852ff4122eec590ede858cc0b77257e3c3))
* 摸牌界面的关闭逻辑 ([08e4b80](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/08e4b80baa4240a45f89bfd34cd14099e4e22273))
* 摸牌消息 ([5705ba9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5705ba9e4a2ba817fb33471b16e00bc3394b403c))
* 日志弹框关闭 ([ecf1da5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ecf1da5ade49a7e912b17d6f267dc8f10cf63cee))
* 上下牙齿分布 ([2c34da8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2c34da8aa84bfe4aa714294ee710bf9590189ccb))
* 上下牙齿分布 ([71728db](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/71728dbad0696a79cc10aebf6de970a4a33e71f9))
* 逃生界面调整 ([30b4b22](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/30b4b22eaecee030b438fde9e96ce05fb5beb26a))
* 桶子还原缩小逻辑 ([158eaca](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/158eaca38e210afda41e6a4f3b846e1a40f56456))
* 投票时快速点击切换卡牌 ([4428395](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4428395bdf6af63460b1b1297f2a21646560583d))
* 修复按牙问题 ([b444597](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b4445970e33dc531b6b4ce431ec8587ade979af9))
* 修复绷带状态导致木桶放大后没缩小 ([47c9f93](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/47c9f93a49e66679f808e14c4ce7b707811d5244))
* 修复场景报错 ([6c29dba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6c29dba9333421767871b08b86c971d1b33a12ac))
* 修复场景报错 ([0d69966](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0d69966a1285c51d2e8cf2bd5fade2ebac853e83))
* 修复后台切前台后可能shine节点没隐藏 ([a92692c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a92692c51dc602ebcac7d6816a21c9c90ce6094a))
* 修复互中诅咒的场景，轮到第二个人时按牙卡住 ([5915e25](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5915e25f1981cbddc90c6e8d7f1aa63e8a698cb2))
* 修复交换到一张交换牌的场景 ([7e41649](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7e41649e0f574ffec57b005706c4daa2e4befd18))
* 修复切后台后返回手牌可能层级不对 ([820f4be](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/820f4be2de910c6f74ff6d82aee7137ddc9c0d9b))
* 修复切后台后返回手牌可能层级不对 ([a6814f9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a6814f9b6da268279f2888ef177c12d4e8a4d7eb))
* 修复手牌打出到出牌区仍可点击 ([47d5a98](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/47d5a98218c67da9598fa7e3641a0ad49085880c))
* 修复同时中诅咒和多个锅的状态 ([86d19b8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/86d19b86ef941f1d05f98b571240bf83704f098d))
* 修复投票组件缩略模式布局 ([7de16e5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7de16e5ba363b4b74e7fbc8eb9adc582a764a56e))
* 修复运行时报错 ([5c8f404](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5c8f404f0b84b2d142a70d94f844e84c6359a810))
* 修复运行时报错 ([93cfd1a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/93cfd1a41e42f6fa7a2c4a24102d9ce403d82142))
* 修复只剩最后一人交换时动效卡住 ([14d3a83](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/14d3a8325d3aca2c7aa6619ee510babe81ce2bd9))
* 修复指定人界面诅咒数显示延迟 ([b6c915b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b6c915b9468296d84ae991b8e5f4d637a991b9ab))
* 修复中了2重甩锅后摸到坏牙的场景 ([3b1ef6f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3b1ef6ffc75fa5c149d9cf13dab9f32b7dfa1e26))
* 用户信息变更和观战 ([6e9db5e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6e9db5e07dec43cfc5fc2b352889b331571c2bd2))
* 支持url传参进入游戏流程 ([0649c11](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0649c11b0c7e25f35f484031aaf7580dc6d39108))
* 诅咒选人列表去除自己 ([3f5bb0e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3f5bb0e686545df91ec9cc2bf2262f34096a6a99))
* 坐下第二颗牙无法点击 ([0055909](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0055909f6a3e6067cf1db28f8bd36142c105e893))
* add core-js ([e4c44c4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e4c44c412a1a835bfed9a3bf27f4143cd5d35060))
* avatar ([f7db817](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f7db817d34bbf80f65eda4673111daeda7378292))
* avatar ([fa217d9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa217d9488c7b7cd6372137d9446491c05abfa40))
* avatar ([46318b1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/46318b1e4168282d807ac0ddbf4a0f0f2cf382a7))
* cardItem 层级问题 ([baf9b29](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/baf9b295d872dd120bc70eb56e71c07f43eb6cf0))
* Circular Reference ([4740509](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4740509ae1b1200e74468dbf8111d1b1031582cc))
* Circular Reference ([ce62a90](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ce62a909650b048a028715463e53c4efea2f8f75))
* debug环境判断去掉 local ([e69591f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e69591fc4859e4c97d0857007efb94155675aa5f))
* error ([0cf45cd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0cf45cd54c8a5941236c182d2b8881756684fe3b))
* error ([2472062](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2472062e8f80aca231913fc7a51070466dfb343f))
* gui层调整，修复后台切前台后的遮罩层问题 ([93d14fc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/93d14fc30106730711022fc0cb6a499376517a0c))
* loading ([5902c77](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5902c77739c17c11587b1273cf4fdace7e014ead))
* log ([bf558de](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bf558de1344e08dbf68e91911885f3e92a27de6e))
* memory leak ([916c8fc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/916c8fc26a3168ade88fa72429a92d10e164475f))
* stringify ([7cf2ff5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7cf2ff5e3575b38158307cf9243bd3f96346ab57))
* type error ([d6c85ed](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d6c85ed596e51a2c71ef73d21ba4349a7193b144))
* type error ([43160ea](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/43160ea02a69aaeb757f8c6edf7b77ea637c9f4c))
* ws请求错误不再抛给业务 ([d15aba1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d15aba1c301c6f104064b5dc5d4fef11b60f440e))


### 🔧 其他更改

* 打包命令升级 ([87481e9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/87481e9d9904801611509c97de1f2b8e6cee7502))
* 复制小心鲨手 core 目录 ([71165fa](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/71165fa4f3db283fecbd0bca01b7bb58ab3b1bd0))
* 海盗桶仓库改名 ([d5263d8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d5263d8b9308b203ed35e2400698c9d618fdb758))
* 恢复裁剪过度的功能。。 ([7dcfc9a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7dcfc9a1e25d46dfcc259e64c5b6649e6e31564f))
* 恢复裁剪过度的功能。。 ([23ed41e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/23ed41e89c80c8043d81939459f036d489387546))
* 恢复裁剪过度的功能。。 ([a88f943](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a88f9434d80d0e4828aeec80632b7b18a71c8d4f))
* 恢复裁剪过度的功能。。 ([d26d171](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d26d171e2698cfb67bee35b77890398eb1594456))
* 升级cocos版本 ([b4fce96](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b4fce96037bc07f269126029196f765f1e72d10a))
* 升级proto依赖 ([2ed818b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2ed818b0b49c96ac0395c64c4cd4fff528da073f))
* 升级proto依赖 ([ab5c95e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ab5c95e60f262647dc686be06160e7a9621b82f7))
* 升级proto依赖 ([f0c3f38](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f0c3f387a7335f93be22ebb26b361eaa02557604))
* 升级proto依赖 ([6778f12](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6778f12830112c605d41f9a405cfed47013c1b1e))
* 项目设置优化 ([8d7432d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d7432dd78d8248af673174d62dedc152b04a3a3))
* 小心鲨手升级cocos ([52bc6b9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/52bc6b9dac891edbfc6867b83ed426ce5bfa393c))
* 引擎功能裁剪 ([bbad1c3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bbad1c30427fe88686718effe4b01773ecf9313b))
* 引擎功能进一步裁剪 ([1fd363b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1fd363b259c464bf05b717494eb27a42c31407ba))
* cli ([427fe59](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/427fe595046d662faed1702c3d75da9d4d192c6d))
* cli ([93f9746](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/93f97465319b1f2b6b7943c6e3a796ac23d39ba1))
* cli ([58e7945](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/58e7945d20ec5867ecf512bb24405dbc105bd784))
* cli 工具 ([9b94ffc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9b94ffc097836af62bd30e7aba3b23b1242d8c16))
* cli 优化 ([2468bc3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2468bc33c5ba5b90544f5c06fe9e66462ed3e8f7))
* mobx-tsuki ([bbe9c24](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bbe9c2423c685fc14817a430261c48b445d97472))
* standard-version ([8b3244f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8b3244ff297d4283aa1032015239f40de9156322))
* standard-version ([e22417e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e22417e00a44feb1d080a01237ac1cc000250804))
* standard-version ([cb581af](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cb581afc44915f9a126ba1bbd2c69b5bb721e731))
* standard-version ([4fc668a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4fc668a4501e1705b0bdce8f3b9fe61ae0c5cc09))
* standard-version ([a3c864b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a3c864bb8cab6ff6be3e855c57fca72fbc811a0f))
* update pb ([355bad2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/355bad2ad0c13e9a77398292d0c59b710a4ca98b))
* update pb ([64ba500](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/64ba500a8f26e1a16ac2cc244a35a85c1ba61d56))
* update pb ([0874e3f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0874e3f0f3ea8fd7a6e531c53c73d223abac582c))
* update pb ([3f073ce](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3f073ceb695b6c1d5616e0d5a976586e37f9b49c))
* upgrade pb ([a0adbba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a0adbbaf50db29ed71d433d960ab9da9f3d7fcbd))
* upgrade pb ([0fe5d64](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0fe5d64f660eb2096a784f212397433800e1c17c))
* upgrade pb ([1b64e98](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1b64e983b5f70a3f05c086d212f1bd17df30b0a4))
* upgrade pb ([1b2986a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1b2986a84195e970316a9e8925f350d170f1634b))

# 小心鲨手 - 更新日志

本项目的所有重要更改都将记录在此文件中。

本文件格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始化版本管理系统
- 集成 standard-version 进行自动化版本管理

### 变更
- 无

### 修复
- 无

### 移除
- 无

---

*注意：此 CHANGELOG 将在首次运行 standard-version 后自动更新*
