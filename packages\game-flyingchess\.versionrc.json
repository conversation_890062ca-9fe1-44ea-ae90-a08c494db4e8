{"types": [{"type": "feat", "section": "✨ 新功能"}, {"type": "fix", "section": "🐛 Bug修复"}, {"type": "docs", "section": "📝 文档更新"}, {"type": "style", "section": "💄 代码格式"}, {"type": "refactor", "section": "♻️ 代码重构"}, {"type": "perf", "section": "⚡ 性能优化"}, {"type": "test", "section": "✅ 测试"}, {"type": "build", "section": "📦 构建系统"}, {"type": "ci", "section": "👷 CI/CD"}, {"type": "chore", "section": "🔧 其他更改"}], "commitUrlFormat": "{{host}}/{{owner}}/{{repository}}/commit/{{hash}}", "compareUrlFormat": "{{host}}/{{owner}}/{{repository}}/compare/{{previousTag}}...{{currentTag}}", "issueUrlFormat": "{{host}}/{{owner}}/{{repository}}/issues/{{id}}", "userUrlFormat": "{{host}}/{{user}}", "releaseCommitMessageFormat": "chore(game-flyingchess): release {{currentTag}}", "tagPrefix": "game-flyingchess@", "skip": {"bump": false, "changelog": false, "commit": false, "tag": false}, "infile": "CHANGELOG.md", "header": "# 飞行棋 - 更新日志\n\n本项目的所有重要更改都将记录在此文件中。\n\n", "packageFiles": [{"filename": "package.json", "type": "json"}], "bumpFiles": [{"filename": "package.json", "type": "json"}], "path": "packages/game-flyingchess/"}